// lib/types.ts

export interface ServiceData {
  service_name: string;
  service_slug: string;
  service_category: string;
  service_type: string;
  city: string;
  city_slug: string;
  state: string;
  phone: string;
  emergency_available: boolean;
  response_time: string;
  base_description: string;
  priority_level: string;
}

export interface AIGeneratedContent {
  localChallenges: string[];
  serviceProcess: string[];
  emergencySteps: string[];
  localLandmarks: string[];
  seasonalTips: string[];
  propertyTypes: string[];
  responseTime: string;
  certifications: string[];
  equipmentUsed: string[];
  commonCauses: string[];
  preventionTips: string[];
  insuranceInfo: string[];
  whyChooseUs: string[];
  serviceAreas: string[];
  relatedServices: string[];
}

export interface EnhancedServiceData extends ServiceData {
  aiContent: AIGeneratedContent;
}

export interface LocationData {
  city: string;
  city_slug: string;
  state: string;
  phone: string;
  services: ServiceData[];
  aiContent: {
    cityOverview: string;
    commonDamageTypes: string[];
    localChallenges: string[];
    landmarks: string[];
    neighborhoods: string[];
    emergencyInfo: string[];
    seasonalConsiderations: string[];
    propertyTypes: string[];
  };
}

export interface ServiceCategoryData {
  category: string;
  category_slug: string;
  services: ServiceData[];
  aiContent: {
    categoryOverview: string;
    commonScenarios: string[];
    processOverview: string[];
    equipmentUsed: string[];
    certifications: string[];
    whyImportant: string[];
  };
}

export interface MetaData {
  title: string;
  description: string;
  keywords: string[];
  canonical: string;
  ogTitle: string;
  ogDescription: string;
  structuredData: any;
}

export interface PageData {
  meta: MetaData;
  content: any;
  breadcrumbs: Array<{
    label: string;
    href: string;
  }>;
}
