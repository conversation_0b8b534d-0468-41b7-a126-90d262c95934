{"name": "mm-restoration", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "generate-data": "tsx scripts/generate-data.ts", "generate-sitemaps": "tsx scripts/generate-sitemaps.ts", "build-all": "npm run generate-data && npm run build"}, "dependencies": {"next": "14.2.23", "react": "^18", "react-dom": "^18", "openai": "^4.0.0", "p-limit": "^5.0.0", "@radix-ui/react-slot": "^1.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "lucide-react": "^0.400.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "postcss": "^8", "tailwindcss": "^3.3.0", "eslint": "^8", "eslint-config-next": "14.2.23", "tsx": "^4.0.0"}}