// scripts/generate-data.ts
import fs from 'fs/promises';
import path from 'path';
import OpenAI from 'openai';
import pLimit from 'p-limit';
import { ServiceData, AIGeneratedContent, EnhancedServiceData, LocationData, ServiceCategoryData } from '../lib/types';

const ITEMS_PER_MINUTE = 30000;
const BATCH_SIZE = 300;
const DELAY_BETWEEN_BATCHES = Math.floor(60000 / (ITEMS_PER_MINUTE / BATCH_SIZE));

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || 'your_api_key_here',
});

const limit = pLimit(BATCH_SIZE);

async function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function readCSVData(): Promise<ServiceData[]> {
  try {
    const csvPath = path.join(process.cwd(), 'data', 'data.csv');
    const csvContent = await fs.readFile(csvPath, 'utf-8');
    const lines = csvContent.trim().split('\n');
    const headers = lines[0].split(',');
    
    const data: ServiceData[] = [];
    
    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',');
      const row: any = {};
      
      headers.forEach((header, index) => {
        const value = values[index]?.trim();
        if (header === 'emergency_available') {
          row[header] = value === 'true';
        } else {
          row[header] = value;
        }
      });
      
      data.push(row as ServiceData);
    }
    
    console.log(`Loaded ${data.length} records from CSV`);
    return data;
  } catch (error) {
    console.error('Error reading CSV:', error);
    return [];
  }
}

async function getServiceAIContent(serviceData: ServiceData): Promise<AIGeneratedContent> {
  const prompt = `Generate detailed restoration service information for "${serviceData.service_name}" in "${serviceData.city}, MI" in JSON format.

Example output format:
{
  "localChallenges": ["Basement flooding common in spring", "Older home foundation issues"],
  "serviceProcess": ["Emergency assessment", "Water extraction", "Drying and dehumidification", "Restoration"],
  "emergencySteps": ["Turn off water source", "Remove standing water if safe", "Call M&M Restoration"],
  "localLandmarks": ["Near downtown ${serviceData.city}", "Serving ${serviceData.city} area"],
  "seasonalTips": ["Winter pipe prevention", "Spring flooding preparation"],
  "propertyTypes": ["Residential homes", "Commercial buildings", "Industrial facilities"],
  "responseTime": "${serviceData.response_time} emergency response",
  "certifications": ["IICRC certified", "Licensed and insured"],
  "equipmentUsed": ["Industrial dehumidifiers", "Water extraction pumps"],
  "commonCauses": ["Pipe bursts", "Appliance leaks", "Storm damage"],
  "preventionTips": ["Regular maintenance", "Monitor water pressure"],
  "insuranceInfo": ["Work with all insurance companies", "Direct billing available"],
  "whyChooseUs": ["24/7 availability", "Licensed technicians", "Advanced equipment"],
  "serviceAreas": ["${serviceData.city} and surrounding areas"],
  "relatedServices": ["Mold remediation", "Fire damage cleanup"]
}

Consider these data points:
- Service: ${serviceData.service_name}
- City: ${serviceData.city}, MI
- Emergency Available: ${serviceData.emergency_available}
- Response Time: ${serviceData.response_time}
- Service Category: ${serviceData.service_category}
- Priority Level: ${serviceData.priority_level}`;

  try {
    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [{
        role: "system",
        content: "You are an expert in damage restoration services. Provide detailed, accurate information in JSON format for local restoration companies."
      }, {
        role: "user",
        content: prompt
      }],
      response_format: { type: "json_object" },
      temperature: 0.7,
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
      throw new Error('No content received from OpenAI');
    }

    return JSON.parse(content);
  } catch (error) {
    console.error(`Error getting AI content for ${serviceData.service_name} in ${serviceData.city}:`, error);
    return {
      localChallenges: [`Common ${serviceData.service_category} issues in ${serviceData.city}`],
      serviceProcess: ["Assessment", "Service delivery", "Restoration", "Follow-up"],
      emergencySteps: ["Contact M&M Restoration immediately", "Secure the area", "Document damage"],
      localLandmarks: [`Serving ${serviceData.city}, MI area`],
      seasonalTips: ["Regular maintenance recommended"],
      propertyTypes: ["Residential", "Commercial"],
      responseTime: serviceData.response_time,
      certifications: ["Licensed and insured"],
      equipmentUsed: ["Professional equipment"],
      commonCauses: ["Various causes"],
      preventionTips: ["Regular maintenance"],
      insuranceInfo: ["Insurance claims assistance"],
      whyChooseUs: ["Professional service", "Licensed technicians"],
      serviceAreas: [serviceData.city],
      relatedServices: ["Related restoration services"]
    };
  }
}

async function processServiceData(services: ServiceData[]): Promise<EnhancedServiceData[]> {
  const processedServices: EnhancedServiceData[] = [];
  let processedCount = 0;
  const startTime = Date.now();
  let lastLogTime = startTime;

  console.log(`Processing ${services.length} service records...`);

  for (let i = 0; i < services.length; i += BATCH_SIZE) {
    const batch = services.slice(i, i + BATCH_SIZE);

    try {
      const batchResults = await Promise.all(
        batch.map(service => limit(() => getServiceAIContent(service)))
      );
      
      processedServices.push(...batch.map((service, index) => ({
        ...service,
        aiContent: batchResults[index]
      })));
      
      processedCount += batch.length;

      // Log progress every 10 seconds
      const currentTime = Date.now();
      if (currentTime - lastLogTime >= 10000 || processedCount === services.length) {
        const elapsedMinutes = (currentTime - startTime) / 60000;
        const itemsPerMinute = Math.round(processedCount / elapsedMinutes);
        const progress = Math.floor((processedCount / services.length) * 100);
        const estimatedTotalMinutes = services.length / itemsPerMinute;
        const remainingMinutes = Math.max(0, estimatedTotalMinutes - elapsedMinutes);
        
        console.log(
          `Progress: ${progress}% | ` +
          `${processedCount.toLocaleString()}/${services.length.toLocaleString()} items | ` +
          `${itemsPerMinute.toLocaleString()}/minute | ` +
          `~${Math.round(remainingMinutes)}m remaining`
        );
        lastLogTime = currentTime;
      }

      if (i + BATCH_SIZE < services.length) {
        await sleep(DELAY_BETWEEN_BATCHES);
      }
    } catch (error) {
      console.error('Error processing batch:', error);
      continue;
    }
  }

  return processedServices;
}

async function ensureDirectoryExists(dirPath: string): Promise<void> {
  try {
    await fs.access(dirPath);
  } catch {
    await fs.mkdir(dirPath, { recursive: true });
  }
}

async function generateIndividualServiceFiles(services: EnhancedServiceData[]): Promise<void> {
  const servicesDir = path.join(process.cwd(), 'data', 'services');
  await ensureDirectoryExists(servicesDir);

  const serviceMap = new Map<string, EnhancedServiceData[]>();
  
  // Group services by service_slug
  services.forEach(service => {
    if (!serviceMap.has(service.service_slug)) {
      serviceMap.set(service.service_slug, []);
    }
    serviceMap.get(service.service_slug)!.push(service);
  });

  for (const [serviceSlug, serviceList] of serviceMap) {
    const serviceFile = path.join(servicesDir, `${serviceSlug}.json`);
    await fs.writeFile(serviceFile, JSON.stringify({
      service_slug: serviceSlug,
      service_name: serviceList[0].service_name,
      service_category: serviceList[0].service_category,
      service_type: serviceList[0].service_type,
      locations: serviceList,
      meta: {
        total_locations: serviceList.length,
        emergency_available: serviceList[0].emergency_available,
        priority_level: serviceList[0].priority_level
      }
    }, null, 2));
  }

  console.log(`Generated ${serviceMap.size} individual service files`);
}

async function generateLocationFiles(services: EnhancedServiceData[]): Promise<void> {
  const locationsDir = path.join(process.cwd(), 'data', 'locations');
  await ensureDirectoryExists(locationsDir);

  const locationMap = new Map<string, EnhancedServiceData[]>();

  // Group services by city_slug
  services.forEach(service => {
    if (!locationMap.has(service.city_slug)) {
      locationMap.set(service.city_slug, []);
    }
    locationMap.get(service.city_slug)!.push(service);
  });

  for (const [citySlug, serviceList] of locationMap) {
    // Generate AI content for the location
    const locationAIContent = await getLocationAIContent(serviceList[0]);

    const locationFile = path.join(locationsDir, `${citySlug}.json`);
    await fs.writeFile(locationFile, JSON.stringify({
      city: serviceList[0].city,
      city_slug: citySlug,
      state: serviceList[0].state,
      phone: serviceList[0].phone,
      services: serviceList,
      aiContent: locationAIContent,
      meta: {
        total_services: serviceList.length,
        emergency_services: serviceList.filter(s => s.emergency_available).length
      }
    }, null, 2));
  }

  console.log(`Generated ${locationMap.size} location files`);
}

async function getLocationAIContent(sampleService: ServiceData): Promise<any> {
  const prompt = `Generate detailed location information for "${sampleService.city}, MI" for a damage restoration company in JSON format.

Example output format:
{
  "cityOverview": "Overview of ${sampleService.city}, Michigan and its restoration service needs",
  "commonDamageTypes": ["Water damage from aging infrastructure", "Storm damage from severe weather"],
  "localChallenges": ["Basement flooding in older homes", "Ice dam issues in winter"],
  "landmarks": ["Downtown ${sampleService.city}", "Local business district"],
  "neighborhoods": ["Historic district", "Residential areas"],
  "emergencyInfo": ["24/7 emergency response available", "Fast response times"],
  "seasonalConsiderations": ["Winter pipe freeze prevention", "Spring flooding preparation"],
  "propertyTypes": ["Single-family homes", "Commercial buildings", "Apartments"]
}

Consider these data points:
- City: ${sampleService.city}, MI
- State: ${sampleService.state}
- Phone: ${sampleService.phone}`;

  try {
    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [{
        role: "system",
        content: "You are an expert in Michigan geography and local property damage restoration needs."
      }, {
        role: "user",
        content: prompt
      }],
      response_format: { type: "json_object" },
      temperature: 0.7,
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
      throw new Error('No content received from OpenAI');
    }

    return JSON.parse(content);
  } catch (error) {
    console.error(`Error getting location AI content for ${sampleService.city}:`, error);
    return {
      cityOverview: `${sampleService.city}, Michigan restoration services`,
      commonDamageTypes: ["Water damage", "Storm damage"],
      localChallenges: ["Weather-related damage"],
      landmarks: [`Downtown ${sampleService.city}`],
      neighborhoods: ["Residential areas"],
      emergencyInfo: ["24/7 emergency response"],
      seasonalConsiderations: ["Weather considerations"],
      propertyTypes: ["Residential", "Commercial"]
    };
  }
}

async function generateCombinationFiles(services: EnhancedServiceData[]): Promise<void> {
  const combinationsDir = path.join(process.cwd(), 'data', 'combinations');
  await ensureDirectoryExists(combinationsDir);

  for (const service of services) {
    const combinationSlug = `${service.service_slug}-${service.city_slug}`;
    const combinationFile = path.join(combinationsDir, `${combinationSlug}.json`);

    await fs.writeFile(combinationFile, JSON.stringify({
      service_name: service.service_name,
      service_slug: service.service_slug,
      city: service.city,
      city_slug: service.city_slug,
      combination_slug: combinationSlug,
      ...service,
      meta: {
        page_type: 'service_location_combination',
        target_keywords: [
          `${service.service_name.toLowerCase()} ${service.city.toLowerCase()} mi`,
          `${service.service_name.toLowerCase()} ${service.city.toLowerCase()} michigan`,
          `emergency ${service.service_name.toLowerCase()} ${service.city.toLowerCase()}`
        ]
      }
    }, null, 2));
  }

  console.log(`Generated ${services.length} service × location combination files`);
}

async function generateCategoryFiles(services: EnhancedServiceData[]): Promise<void> {
  const categoriesDir = path.join(process.cwd(), 'data', 'categories');
  await ensureDirectoryExists(categoriesDir);

  const categoryMap = new Map<string, EnhancedServiceData[]>();

  // Group services by service_category
  services.forEach(service => {
    if (!categoryMap.has(service.service_category)) {
      categoryMap.set(service.service_category, []);
    }
    categoryMap.get(service.service_category)!.push(service);
  });

  for (const [category, serviceList] of categoryMap) {
    // Generate AI content for the category
    const categoryAIContent = await getCategoryAIContent(category, serviceList);

    const categoryFile = path.join(categoriesDir, `${category}-services.json`);
    await fs.writeFile(categoryFile, JSON.stringify({
      category,
      category_slug: `${category}-services`,
      services: serviceList,
      aiContent: categoryAIContent,
      meta: {
        total_services: serviceList.length,
        service_types: [...new Set(serviceList.map(s => s.service_name))]
      }
    }, null, 2));
  }

  console.log(`Generated ${categoryMap.size} category files`);
}

async function getCategoryAIContent(category: string, services: EnhancedServiceData[]): Promise<any> {
  const serviceNames = services.map(s => s.service_name).join(', ');

  const prompt = `Generate detailed category information for "${category}" restoration services in JSON format.

Example output format:
{
  "categoryOverview": "Comprehensive overview of ${category} restoration services",
  "commonScenarios": ["Emergency situations", "Planned restoration"],
  "processOverview": ["Assessment", "Planning", "Execution", "Completion"],
  "equipmentUsed": ["Professional equipment", "Specialized tools"],
  "certifications": ["IICRC certification", "State licensing"],
  "whyImportant": ["Health and safety", "Property protection"]
}

Consider these services in this category:
- Services: ${serviceNames}
- Category: ${category}`;

  try {
    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [{
        role: "system",
        content: "You are an expert in damage restoration services and industry standards."
      }, {
        role: "user",
        content: prompt
      }],
      response_format: { type: "json_object" },
      temperature: 0.7,
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
      throw new Error('No content received from OpenAI');
    }

    return JSON.parse(content);
  } catch (error) {
    console.error(`Error getting category AI content for ${category}:`, error);
    return {
      categoryOverview: `Professional ${category} services`,
      commonScenarios: ["Emergency response", "Scheduled service"],
      processOverview: ["Assessment", "Service delivery"],
      equipmentUsed: ["Professional equipment"],
      certifications: ["Licensed and insured"],
      whyImportant: ["Property protection", "Safety"]
    };
  }
}

async function main() {
  try {
    console.log('Starting M&M Restoration data generation...');

    // Read CSV data
    const csvData = await readCSVData();
    if (csvData.length === 0) {
      console.error('No data found in CSV file');
      process.exit(1);
    }

    console.log(`Processing ${csvData.length} service records at ${ITEMS_PER_MINUTE} items/minute...`);

    // Process with AI content generation
    const enhancedServices = await processServiceData(csvData);

    // Generate individual service files
    await generateIndividualServiceFiles(enhancedServices);

    // Generate location files
    await generateLocationFiles(enhancedServices);

    // Generate service × location combination files
    await generateCombinationFiles(enhancedServices);

    // Generate category files
    await generateCategoryFiles(enhancedServices);

    console.log('Data generation completed successfully!');
    console.log(`Total files generated:`);
    console.log(`- Service files: ${new Set(enhancedServices.map(s => s.service_slug)).size}`);
    console.log(`- Location files: ${new Set(enhancedServices.map(s => s.city_slug)).size}`);
    console.log(`- Combination files: ${enhancedServices.length}`);
    console.log(`- Category files: ${new Set(enhancedServices.map(s => s.service_category)).size}`);

  } catch (error) {
    console.error('Fatal error:', error);
    process.exit(1);
  }
}

// Error handling
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (error) => {
  console.error('Unhandled Rejection:', error);
  process.exit(1);
});

// Run the script
if (require.main === module) {
  main();
}
